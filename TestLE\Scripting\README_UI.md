# Lua Script UI

A simple UI for managing Lua scripts in the TestLE mod. Follows the KISS principle - just select a script and start/stop it.

## Features

- **Script Selection**: Click the script name button to cycle through available scripts, or use < > buttons
- **Start/Stop Control**: Simple buttons to start and stop the selected script
- **Status Display**: Shows current running status and script count
- **Auto-refresh**: Script list refreshes every 2 seconds to pick up new files
- **Hot Reload**: Scripts automatically reload when files change (via LuaManager)

## Controls

- **F8**: Toggle main UI (includes Lua UI)
- **F9**: Toggle Lua UI only
- **Script Button**: Click to cycle through available scripts
- **< / >**: Navigate to previous/next script
- **Start**: Start the selected script (calls OnUpdate function)
- **Stop**: Stop the currently running script
- **Refresh**: Manually refresh the script list

## Usage

1. Place your `.lua` scripts in the `Mods/TestLE/Scripts/` directory
2. Press F8 to show the main UI (if not already visible)
3. The Lua UI will appear at the top
4. Click the script name to select a script
5. Click "Start" to begin running the script
6. Click "Stop" to stop the script

## Script Requirements

Your Lua scripts should have an `OnUpdate()` function that will be called repeatedly while the script is running:

```lua
function OnUpdate()
    -- Your script logic here
    Log("Script is running!")
end
```

## Example Scripts

- `UITestScript.lua` - Simple test script that logs player info every 2 seconds
- `ExampleBasicScript.lua` - Basic functionality demonstration
- `ExampleCombatScript.lua` - Combat monitoring example
- `ExampleLootScript.lua` - Loot detection example

## Notes

- Only one script can run at a time through the UI
- Scripts automatically reload when you save changes to the `.lua` files
- The UI shows the current status (Running/Stopped) with color coding
- Script list updates automatically every 2 seconds
- If a script has errors, it will stop automatically and log the error
