# TestLE Lua Coroutines

The TestLE Lua scripting system now uses **MoonSharp coroutines** instead of calling `OnUpdate()` every Unity frame. This provides better performance and more flexible script execution patterns.

## Key Changes

### Before (OnUpdate-based)
```lua
function OnUpdate()
    -- This was called EVERY Unity frame (60+ times per second)
    if Time() - lastTime > 5.0 then
        -- Do something every 5 seconds
        lastTime = Time()
    end
end
```

### After (Coroutine-based)
```lua
function main()
    -- This runs as a coroutine
    while true do
        -- Do something
        waitForSeconds(5.0)  -- Yield for 5 seconds
    end
end
```

## Benefits

1. **Better Performance**: Scripts only run when needed, not every frame
2. **Cleaner Code**: No need for manual timing logic
3. **More Control**: Scripts can yield execution and resume later
4. **Flexible Timing**: Easy to implement different update intervals per script

## New Script Structure

Scripts must now define a `main()` function instead of `OnUpdate()`:

```lua
function main()
    Log("Script starting...")
    
    while true do
        -- Your script logic here
        
        -- Yield control back to the system
        waitForSeconds(1.0)  -- Wait 1 second
        -- or
        yield()              -- Yield until next update cycle
        -- or  
        waitForNextFrame()   -- Wait for next frame
    end
end
```

## Available Coroutine Functions

### `waitForSeconds(seconds)`
Pauses the coroutine for the specified number of seconds.
```lua
waitForSeconds(5.0)  -- Wait 5 seconds
```

### `yield()`
Yields control back to the coroutine manager. The coroutine will resume on the next update cycle.
```lua
yield()  -- Resume on next update
```

### `waitForNextFrame()`
Equivalent to `yield()` - waits for the next frame.
```lua
waitForNextFrame()
```

### `shouldYield()`
Utility function that returns `true` every 10 frames. Useful for manual yielding in loops.
```lua
for i = 1, 1000 do
    -- Do work
    if shouldYield() then
        yield()
    end
end
```

## Example Scripts

### Basic Example
```lua
function main()
    local counter = 0
    
    while true do
        counter = counter + 1
        Log("Counter: " .. counter)
        
        -- Wait 2 seconds between iterations
        waitForSeconds(2.0)
    end
end
```

### Multiple Tasks Pattern
```lua
function main()
    -- Start background tasks
    startMonitoring()
    
    -- Main loop
    while true do
        -- Main logic
        waitForSeconds(10.0)
    end
end

function startMonitoring()
    while true do
        -- Monitor something
        checkHealth()
        waitForSeconds(1.0)
    end
end

function checkHealth()
    local health = GetPlayerHealth()
    if health and health < 50 then
        Log("Low health warning!")
    end
end
```

## Performance Notes

- Coroutines are updated every 100ms (10 times per second) instead of every frame
- This reduces CPU usage significantly compared to the old OnUpdate system
- Scripts that need frame-perfect timing should use `waitForNextFrame()` sparingly
- Most game logic doesn't need frame-perfect timing and benefits from the reduced update frequency

## Migration Guide

To convert existing OnUpdate scripts to coroutines:

1. Rename `OnUpdate()` to `main()`
2. Add a `while true do` loop around your main logic
3. Replace timing logic with `waitForSeconds()`
4. Remove manual time tracking variables

### Before
```lua
local lastTime = 0

function OnUpdate()
    local currentTime = Time()
    if currentTime - lastTime < 5.0 then
        return
    end
    lastTime = currentTime
    
    -- Do work
    Log("Working...")
end
```

### After
```lua
function main()
    while true do
        -- Do work
        Log("Working...")
        
        -- Wait 5 seconds
        waitForSeconds(5.0)
    end
end
```

## Example Scripts

Check out these example scripts:
- `CoroutineBasicExample.lua` - Basic coroutine usage
- `CoroutineLootMonitor.lua` - Loot monitoring with coroutines
- `CoroutineCombatMonitor.lua` - Combat monitoring with coroutines
- `CoroutineAdvancedExample.lua` - Advanced patterns and multiple tasks
