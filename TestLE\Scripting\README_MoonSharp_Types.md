# MoonSharp Type Registration - Proper Implementation

This document explains the proper way to register types with MoonSharp for C# to Lua interop.

## ✅ **What We Fixed**

### **Problem**
The original code used `UserData.RegisterAssembly()` which calls `Assembly.GetCallingAssembly()` - not supported in MelonLoader environments.

### **Solution**
Implemented proper explicit type registration using `UserData.RegisterType<T>()` for each specific type.

## 🔧 **Implementation Details**

### **1. Type Registration (LuaAPI.cs)**

```csharp
/// <summary>
/// Register all necessary types with MoonSharp for proper interop.
/// This should be called once before creating any scripts.
/// </summary>
public static void RegisterTypes()
{
    if (_typesRegistered) return;

    try
    {
        // Unity basic types
        UserData.RegisterType<Vector3>();
        UserData.RegisterType<Transform>();
        UserData.RegisterType<GameObject>();
        UserData.RegisterType<Camera>();
        
        // Game-specific types that might be exposed to Lua
        UserData.RegisterType<LocalPlayer>();
        UserData.RegisterType<Enemy>();
        UserData.RegisterType<GroundItem>();
        UserData.RegisterType<WorldObjectClickListener>();
        
        // Collections (if needed)
        UserData.RegisterType<List<Enemy>>();
        UserData.RegisterType<List<GroundItem>>();
        UserData.RegisterType<List<WorldObjectClickListener>>();

        _typesRegistered = true;
        MelonLogger.Msg("MoonSharp types registered successfully");
    }
    catch (Exception ex)
    {
        MelonLogger.Error($"Failed to register MoonSharp types: {ex.Message}");
    }
}
```

### **2. Proper UserData Creation**

Instead of passing raw objects, we now create proper UserData instances:

```csharp
// OLD (problematic)
script.Globals["GetPlayer"] = (Func<object?>)(() => PLAYER);

// NEW (proper)
script.Globals["GetPlayer"] = (Func<DynValue>)(() => 
    PLAYER != null ? UserData.Create(PLAYER) : DynValue.Nil);
```

### **3. Type-Safe Return Values**

Functions now return proper MoonSharp types:

```csharp
// Vector3 handling
script.Globals["GetPlayerPosition"] = (Func<DynValue>)(() => {
    var pos = PlayerHelpers.GetPlayerPosition();
    return pos.HasValue ? UserData.Create(pos.Value) : DynValue.Nil;
});

// Numeric values
script.Globals["GetPlayerHealth"] = (Func<DynValue>)(() => {
    var health = PlayerHelpers.GetPlayerHealth();
    return health.HasValue ? DynValue.NewNumber(health.Value) : DynValue.Nil;
});

// Vector3 creation
script.Globals["Vector3"] = (Func<double, double, double, DynValue>)((x, y, z) => 
    UserData.Create(new Vector3((float)x, (float)y, (float)z)));
```

### **4. Safe Distance Calculation**

```csharp
script.Globals["Distance"] = (Func<DynValue, DynValue, double>)((v1, v2) => {
    try {
        var vec1 = v1.UserData.Object as Vector3? ?? Vector3.zero;
        var vec2 = v2.UserData.Object as Vector3? ?? Vector3.zero;
        return Vector3.Distance(vec1, vec2);
    } catch {
        return 0.0;
    }
});
```

## 📋 **Best Practices**

### **1. Register Types Once**
- Use a static flag to ensure types are only registered once
- Call `RegisterTypes()` before creating any scripts
- Handle registration errors gracefully

### **2. Use Explicit Registration**
- Never use `UserData.RegisterAssembly()` in MelonLoader
- Register each type explicitly with `UserData.RegisterType<T>()`
- Only register types that will actually be used in scripts

### **3. Proper UserData Creation**
- Use `UserData.Create(object)` for complex types
- Use `DynValue.NewNumber()` for numeric values
- Use `DynValue.Nil` for null values
- Use `DynValue.NewString()` for strings

### **4. Error Handling**
- Wrap UserData access in try-catch blocks
- Provide sensible defaults when conversion fails
- Log errors for debugging

### **5. Type Safety**
- Check `DynValue.Type` before accessing `.UserData.Object`
- Use safe casting with `as` operator
- Provide fallback values for failed casts

## 🎯 **What Types to Register**

### **Always Register**
- Types you pass to Lua scripts
- Types returned from Lua-callable functions
- Types used as parameters in Lua-callable functions

### **Consider Registering**
- Unity basic types (Vector3, Transform, GameObject)
- Collection types if you pass collections to Lua
- Custom game types that scripts might interact with

### **Don't Register**
- Types never exposed to Lua
- System types that MoonSharp handles automatically
- Types only used internally in C#

## 🔍 **Debugging Tips**

1. **Check Registration**: Ensure all types are registered before use
2. **Verify UserData**: Check if `DynValue.UserData` is not null
3. **Type Checking**: Use `DynValue.Type` to verify expected types
4. **Error Logging**: Log detailed error messages for failed conversions
5. **Test Incrementally**: Add types one at a time to isolate issues

## 📚 **References**

- [MoonSharp Official Documentation](https://www.moonsharp.org/objects.html)
- [UserData Registration Guide](https://www.moonsharp.org/objects.html#keep-it-simple)
- [Type Descriptors](https://www.moonsharp.org/objects.html#lets-have-a-word-about-type-descriptors-first)

This implementation provides robust, type-safe interop between C# and Lua while avoiding the MelonLoader compatibility issues.
