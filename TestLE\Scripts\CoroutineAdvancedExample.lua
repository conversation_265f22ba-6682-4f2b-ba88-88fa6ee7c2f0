-- Advanced Coroutine Example
-- Demonstrates more complex coroutine patterns

function main()
    Log("Starting advanced coroutine example...")
    
    -- Start multiple concurrent tasks
    startTask("playerMonitor")
    startTask("inputHandler")
    startTask("periodicReport")
    
    -- Main loop
    while true do
        -- Main task logic here
        Log("Main coroutine tick")
        waitForSeconds(10.0)
    end
end

function startTask(taskName)
    Log("Starting task: " .. taskName)
    
    if taskName == "playerMonitor" then
        playerMonitorTask()
    elseif taskName == "inputHandler" then
        inputHandlerTask()
    elseif taskName == "periodicReport" then
        periodicReportTask()
    end
end

function playerMonitorTask()
    while true do
        local health = GetPlayerHealth()
        if health and health < 30 then
            Log("CRITICAL: Very low health! (" .. health .. ")")
        end
        
        waitForSeconds(1.0)
    end
end

function inputHandlerTask()
    while true do
        if GetKeyDown("f2") then
            Log("F2 pressed - Emergency stop!")
            break
        end
        
        if GetKeyDown("f3") then
            Log("F3 pressed - Status report")
            statusReport()
        end
        
        -- Check input more frequently
        waitForSeconds(0.1)
    end
end

function periodicReportTask()
    local startTime = Time()
    
    while true do
        local currentTime = Time()
        local elapsed = currentTime - startTime
        
        Log("Periodic report - Elapsed: " .. string.format("%.1f", elapsed) .. "s")
        
        local playerPos = GetPlayerPosition()
        if playerPos then
            Log("Current position: " .. string.format("%.1f, %.1f, %.1f", playerPos.x, playerPos.y, playerPos.z))
        end
        
        waitForSeconds(30.0)
    end
end

function statusReport()
    Log("=== STATUS REPORT ===")
    Log("Time: " .. string.format("%.1f", Time()))
    Log("Frame: " .. FrameCount())
    
    local health = GetPlayerHealth()
    if health then
        Log("Health: " .. health)
    end
    
    local enemies = GetEnemies()
    if enemies then
        Log("Enemies: " .. #enemies)
    end
    
    local items = GetGroundItems()
    if items then
        Log("Ground items: " .. #items)
    end
    
    Log("=== END REPORT ===")
end
