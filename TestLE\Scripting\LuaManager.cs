using MelonLoader;
using MelonLoader.Utils;
using MoonSharp.Interpreter;

namespace TestLE.Scripting;

/// <summary>
/// Lua script manager with MoonSharp coroutine support and hot reloading.
/// </summary>
public class LuaManager
{
    private static LuaManager? _instance;
    public static LuaManager Instance => _instance ??= new LuaManager();

    private readonly Dictionary<string, Script> _scripts = new();
    private readonly string _scriptsDirectory;
    private FileSystemWatcher? _fileWatcher;
    private readonly Dictionary<string, CoroutineState> _runningCoroutines = new();

    public bool IsInitialized { get; private set; }
    public IReadOnlyDictionary<string, Script> Scripts => _scripts;
    public IReadOnlyDictionary<string, CoroutineState> RunningCoroutines => _runningCoroutines;

    /// <summary>
    /// Represents the state of a running Lua coroutine
    /// </summary>
    public class CoroutineState
    {
        public DynValue Coroutine { get; set; }
        public float NextResumeTime { get; set; }
        public bool IsWaiting { get; set; }
        public string ScriptName { get; set; }

        public CoroutineState(DynValue coroutine, string scriptName)
        {
            Coroutine = coroutine;
            ScriptName = scriptName;
            NextResumeTime = 0f;
            IsWaiting = false;
        }
    }

    /// <summary>
    /// Get list of available script names (without .lua extension)
    /// </summary>
    public List<string> GetAvailableScripts()
    {
        if (!Directory.Exists(_scriptsDirectory))
            return new List<string>();

        return Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly)
            .Select(Path.GetFileNameWithoutExtension)
            .Where(name => !string.IsNullOrEmpty(name))
            .Cast<string>()
            .ToList();
    }

    /// <summary>
    /// Get list of currently running script names
    /// </summary>
    public List<string> GetRunningScripts()
    {
        return _runningCoroutines.Keys.ToList();
    }

    private LuaManager()
    {
        _scriptsDirectory = Path.Combine(MelonEnvironment.ModsDirectory, "TestLE", "Scripts");
    }

    public void Initialize()
    {
        if (IsInitialized) return;

        try
        {
            Directory.CreateDirectory(_scriptsDirectory);

            // Register types with MoonSharp first
            LuaAPI.RegisterTypes();

            SetupFileWatcher();
            LoadAllScripts();

            IsInitialized = true;
            MelonLogger.Msg($"LuaManager initialized with {_scripts.Count} scripts");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to initialize LuaManager: {ex.Message}");
        }
    }

    public void Shutdown()
    {
        _fileWatcher?.Dispose();
        _runningCoroutines.Clear();
        _scripts.Clear();
        IsInitialized = false;
    }

    public bool LoadScript(string scriptName)
    {
        try
        {
            var scriptPath = Path.Combine(_scriptsDirectory, $"{scriptName}.lua");
            if (!File.Exists(scriptPath)) return false;

            // Create script with proper CoreModules - work WITH MoonSharp
            var script = new Script(CoreModules.Preset_SoftSandbox);

            // Register our API
            LuaAPI.RegisterAPI(script);

            // Load and execute the script
            script.DoFile(scriptPath);

            _scripts[scriptName] = script;
            MelonLogger.Msg($"Loaded script: {scriptName}");
            return true;
        }
        catch (SyntaxErrorException ex)
        {
            MelonLogger.Error($"Lua syntax error in '{scriptName}': {ex.DecoratedMessage}");
            return false;
        }
        catch (ScriptRuntimeException ex)
        {
            MelonLogger.Error($"Lua runtime error in '{scriptName}': {ex.DecoratedMessage}");
            return false;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to load script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    public DynValue? CallFunction(string scriptName, string functionName, params object[] args)
    {
        if (!_scripts.TryGetValue(scriptName, out var script))
            return null;

        try
        {
            // Work WITH MoonSharp - use the proper Call method
            var function = script.Globals.Get(functionName);
            if (function.IsNil() || function.Type != DataType.Function)
                return null;

            return script.Call(function, args);
        }
        catch (ScriptRuntimeException ex)
        {
            MelonLogger.Error($"Lua runtime error in {scriptName}.{functionName}: {ex.DecoratedMessage}");
            return null;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Error calling {scriptName}.{functionName}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// Start running a script as a coroutine. The script should define a 'main' function that will be run as a coroutine.
    /// </summary>
    public bool StartScript(string scriptName)
    {
        if (string.IsNullOrEmpty(scriptName))
            return false;

        // Check if already running
        if (_runningCoroutines.ContainsKey(scriptName))
            return false;

        // Load the script if not already loaded
        if (!_scripts.ContainsKey(scriptName))
        {
            if (!LoadScript(scriptName))
            {
                MelonLogger.Error($"Failed to load script: {scriptName}");
                return false;
            }
        }

        var script = _scripts[scriptName];

        try
        {
            // Look for a 'main' function to run as coroutine
            var mainFunction = script.Globals.Get("main");
            if (mainFunction.IsNil() || mainFunction.Type != DataType.Function)
            {
                MelonLogger.Error($"Script '{scriptName}' must define a 'main' function to run as coroutine");
                return false;
            }

            // Create and start the coroutine
            var coroutine = script.CreateCoroutine(mainFunction);
            var coroutineState = new CoroutineState(coroutine, scriptName);
            _runningCoroutines[scriptName] = coroutineState;

            MelonLogger.Msg($"Started Lua coroutine: {scriptName}");
            return true;
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to start coroutine for script '{scriptName}': {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Stop running a script coroutine.
    /// </summary>
    public bool StopScript(string scriptName)
    {
        if (!_runningCoroutines.Remove(scriptName))
            return false; // Wasn't running

        MelonLogger.Msg($"Stopped Lua coroutine: {scriptName}");
        return true;
    }

    /// <summary>
    /// Stop all running script coroutines.
    /// </summary>
    public void StopAllScripts()
    {
        foreach (var scriptName in _runningCoroutines.Keys.ToList())
        {
            StopScript(scriptName);
        }
    }

    /// <summary>
    /// Check if a script coroutine is currently running.
    /// </summary>
    public bool IsScriptRunning(string scriptName)
    {
        return _runningCoroutines.ContainsKey(scriptName);
    }

    /// <summary>
    /// Update all running coroutines. This should be called periodically (not every frame).
    /// Coroutines that are waiting will only be resumed when their wait time has elapsed.
    /// </summary>
    public void UpdateRunningCoroutines()
    {
        var currentTime = UnityEngine.Time.time;
        var coroutinesToRemove = new List<string>();

        foreach (var kvp in _runningCoroutines.ToList()) // ToList to avoid modification during iteration
        {
            var scriptName = kvp.Key;
            var state = kvp.Value;

            try
            {
                // Skip if coroutine is waiting and wait time hasn't elapsed
                if (state.IsWaiting && currentTime < state.NextResumeTime)
                    continue;

                // Resume the coroutine
                var result = state.Coroutine.Coroutine.Resume();

                if (result.Type == DataType.YieldRequest)
                {
                    // Handle yield request
                    var yieldRequest = result.YieldRequest;
                    if (yieldRequest != null)
                    {
                        // Check if it's a time-based yield
                        if (yieldRequest.UserData != null && yieldRequest.UserData is float waitTime)
                        {
                            state.IsWaiting = true;
                            state.NextResumeTime = currentTime + waitTime;
                        }
                        else
                        {
                            // Regular yield - resume next update
                            state.IsWaiting = false;
                            state.NextResumeTime = 0f;
                        }
                    }
                }
                else if (result.Type == DataType.Nil || state.Coroutine.Coroutine.State == CoroutineState.Dead)
                {
                    // Coroutine finished
                    coroutinesToRemove.Add(scriptName);
                    MelonLogger.Msg($"Lua coroutine '{scriptName}' completed");
                }
                else
                {
                    // Reset waiting state for next iteration
                    state.IsWaiting = false;
                    state.NextResumeTime = 0f;
                }
            }
            catch (Exception ex)
            {
                MelonLogger.Error($"Error in coroutine {scriptName}: {ex.Message}");
                coroutinesToRemove.Add(scriptName); // Stop the problematic coroutine
            }
        }

        // Remove completed or errored coroutines
        foreach (var scriptName in coroutinesToRemove)
        {
            _runningCoroutines.Remove(scriptName);
        }
    }

    private void LoadAllScripts()
    {
        var luaFiles = Directory.GetFiles(_scriptsDirectory, "*.lua", SearchOption.TopDirectoryOnly);
        foreach (var filePath in luaFiles)
        {
            var scriptName = Path.GetFileNameWithoutExtension(filePath);
            if (!string.IsNullOrEmpty(scriptName))
                LoadScript(scriptName);
        }
    }

    private void SetupFileWatcher()
    {
        try
        {
            _fileWatcher = new FileSystemWatcher(_scriptsDirectory, "*.lua")
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.FileName,
                EnableRaisingEvents = true
            };

            _fileWatcher.Changed += (_, e) => ReloadScript(e);
            _fileWatcher.Created += (_, e) => ReloadScript(e);
            _fileWatcher.Deleted += (_, e) => {
                var name = Path.GetFileNameWithoutExtension(e.Name);
                if (!string.IsNullOrEmpty(name)) _scripts.Remove(name);
            };
        }
        catch (Exception ex)
        {
            MelonLogger.Warning($"Could not set up file watcher: {ex.Message}");
        }
    }

    private void ReloadScript(FileSystemEventArgs e)
    {
        var scriptName = Path.GetFileNameWithoutExtension(e.Name);
        if (!string.IsNullOrEmpty(scriptName) && File.Exists(e.FullPath))
        {
            Task.Delay(100).ContinueWith(_ => {
                MelonLogger.Msg($"Reloading script: {scriptName}");
                LoadScript(scriptName);
            });
        }
    }
}
