-- Proper MoonSharp Coroutine Example
-- Demonstrates correct usage of MoonSharp coroutines following the documentation

function main()
    Log("Starting proper MoonSharp coroutine example...")
    
    -- Example 1: Using standard coroutine.yield()
    demonstrateBasicYield()
    
    -- Example 2: Using custom waitForSeconds()
    demonstrateTimedWait()
    
    -- Example 3: Mixed approach
    demonstrateMixedApproach()
    
    Log("Coroutine example completed")
end

function demonstrateBasicYield()
    Log("=== Basic Yield Example ===")
    
    for i = 1, 5 do
        Log("Basic yield iteration: " .. i)
        -- Use standard MoonSharp coroutine.yield()
        coroutine.yield()
    end
    
    Log("Basic yield example completed")
end

function demonstrateTimedWait()
    Log("=== Timed Wait Example ===")
    
    for i = 1, 3 do
        Log("Timed wait iteration: " .. i .. " at time: " .. string.format("%.1f", Time()))
        -- Use custom waitForSeconds function
        waitForSeconds(2.0)
    end
    
    Log("Timed wait example completed")
end

function demonstrateMixedApproach()
    Log("=== Mixed Approach Example ===")
    
    local counter = 0
    
    while counter < 10 do
        counter = counter + 1
        
        -- Get player info if available
        local player = GetPlayer()
        if player then
            local health = GetPlayerHealth()
            if health then
                Log("Player health: " .. health .. " (iteration " .. counter .. ")")
            end
        end
        
        -- Use different yield strategies based on conditions
        if counter % 3 == 0 then
            Log("Using timed wait (3 seconds)...")
            waitForSeconds(3.0)
        else
            Log("Using basic yield...")
            coroutine.yield()
        end
    end
    
    Log("Mixed approach example completed")
end
