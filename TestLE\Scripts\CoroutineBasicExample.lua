-- Coroutine Basic Example
-- This demonstrates the new coroutine-based approach instead of OnUpdate

function main()
    Log("Starting coroutine basic example...")
    
    local counter = 0
    
    while true do
        counter = counter + 1
        
        -- Get player info
        local player = GetPlayer()
        if player then
            local pos = GetPlayerPosition()
            if pos then
                Log("Player at: " .. pos.x .. ", " .. pos.y .. ", " .. pos.z .. " (Count: " .. counter .. ")")
            end

            local health = GetPlayerHealth()
            if health then
                Log("Player health: " .. health)
            end

            Log("Player alive: " .. tostring(IsPlayerAlive()))
            Log("Player in combat: " .. tostring(IsPlayerInCombat()))
        end

        -- Check for input
        if GetKeyDown("f1") then
            Log("F1 pressed!")
        end

        -- Random number
        Log("Random: " .. Random())
        
        -- Wait for 5 seconds before next iteration
        waitForSeconds(5.0)
    end
end
