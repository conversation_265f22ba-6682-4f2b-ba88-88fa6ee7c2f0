-- Coroutine Combat Monitor
-- Monitors combat state and enemies using coroutines

function main()
    Log("Starting coroutine combat monitor...")
    
    local wasInCombat = false
    
    while true do
        if not IsPlayerAlive() then
            Log("Player is dead, waiting...")
            waitForSeconds(10.0)
        else
            checkCombat()
            -- Check every 2 seconds
            waitForSeconds(2.0)
        end
    end
end

function checkCombat()
    -- Enemy count
    local enemies = GetEnemies()
    if enemies and #enemies > 0 then
        Log("Enemies nearby: " .. #enemies)
        
        -- Count close enemies
        local playerPos = GetPlayerPosition()
        if playerPos then
            local closeEnemies = 0
            for i = 1, #enemies do
                local enemy = enemies[i]
                if enemy and enemy.transform then
                    local distance = Distance(playerPos, enemy.transform.position)
                    if distance < 15.0 then
                        closeEnemies = closeEnemies + 1
                    end
                end
            end
            
            if closeEnemies > 0 then
                Log("Close enemies (< 15 units): " .. closeEnemies)
            end
        end
    end

    -- Health warning
    local health = GetPlayerHealth()
    if health and health < 50 then
        Log("WARNING: Low health! (" .. health .. ")")
    end
end
