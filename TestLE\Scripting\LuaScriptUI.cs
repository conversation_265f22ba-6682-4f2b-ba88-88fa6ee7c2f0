using MelonLoader;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// Simple UI for managing Lua scripts - KISS principle: just select and start/stop scripts.
/// </summary>
public class LuaScriptUI
{
    private string? _selectedScript;
    private List<string> _availableScripts = new();
    private int _selectedScriptIndex = -1;
    private float _lastRefresh;
    private const float REFRESH_INTERVAL = 2f; // Refresh script list every 2 seconds

    /// <summary>
    /// Draw the Lua script UI. Call this from your main OnGUI method.
    /// </summary>
    public void DrawUI()
    {
        if (!LuaManager.Instance.IsInitialized)
        {
            GUILayout.Label("Lua Manager not initialized", GUILayout.Height(20));
            return;
        }

        // Refresh script list periodically
        if (Time.time - _lastRefresh > REFRESH_INTERVAL)
        {
            RefreshScriptList();
            _lastRefresh = Time.time;
        }

        GUILayout.BeginVertical("box");
        GUILayout.Label("Lua Script Control", GUILayout.Height(20));

        // Script selection dropdown
        DrawScriptSelection();

        // Start/Stop controls
        DrawScriptControls();

        // Status display
        DrawStatus();

        GUILayout.EndVertical();
    }

    private void DrawScriptSelection()
    {
        GUILayout.BeginHorizontal();
        GUILayout.Label("Script:", GUILayout.Width(50));

        if (_availableScripts.Count == 0)
        {
            GUILayout.Label("No scripts found", GUILayout.Height(20));
        }
        else
        {
            // Simple dropdown using buttons
            var currentScriptName = _selectedScriptIndex >= 0 && _selectedScriptIndex < _availableScripts.Count 
                ? _availableScripts[_selectedScriptIndex] 
                : "Select Script";

            if (GUILayout.Button(currentScriptName, GUILayout.Width(150), GUILayout.Height(20)))
            {
                // Cycle through scripts
                _selectedScriptIndex = (_selectedScriptIndex + 1) % _availableScripts.Count;
                _selectedScript = _availableScripts[_selectedScriptIndex];
            }

            // Previous script button
            if (GUILayout.Button("<", GUILayout.Width(20), GUILayout.Height(20)) && _availableScripts.Count > 0)
            {
                _selectedScriptIndex = (_selectedScriptIndex - 1 + _availableScripts.Count) % _availableScripts.Count;
                _selectedScript = _availableScripts[_selectedScriptIndex];
            }

            // Next script button  
            if (GUILayout.Button(">", GUILayout.Width(20), GUILayout.Height(20)) && _availableScripts.Count > 0)
            {
                _selectedScriptIndex = (_selectedScriptIndex + 1) % _availableScripts.Count;
                _selectedScript = _availableScripts[_selectedScriptIndex];
            }
        }

        GUILayout.EndHorizontal();
    }

    private void DrawScriptControls()
    {
        GUILayout.BeginHorizontal();

        var isRunning = !string.IsNullOrEmpty(_selectedScript) && LuaManager.Instance.IsScriptRunning(_selectedScript);

        // Start button
        GUI.enabled = !isRunning && !string.IsNullOrEmpty(_selectedScript);
        if (GUILayout.Button("Start", GUILayout.Width(60), GUILayout.Height(25)))
        {
            if (!string.IsNullOrEmpty(_selectedScript))
                LuaManager.Instance.StartScript(_selectedScript);
        }

        // Stop button
        GUI.enabled = isRunning;
        if (GUILayout.Button("Stop", GUILayout.Width(60), GUILayout.Height(25)))
        {
            if (!string.IsNullOrEmpty(_selectedScript))
                LuaManager.Instance.StopScript(_selectedScript);
        }

        GUI.enabled = true;

        // Refresh button
        if (GUILayout.Button("Refresh", GUILayout.Width(60), GUILayout.Height(25)))
        {
            RefreshScriptList();
        }

        GUILayout.EndHorizontal();
    }

    private void DrawStatus()
    {
        var isRunning = !string.IsNullOrEmpty(_selectedScript) && LuaManager.Instance.IsScriptRunning(_selectedScript);
        var statusText = isRunning
            ? $"Running: {_selectedScript}"
            : "Stopped";

        var statusColor = isRunning ? Color.green : Color.gray;

        var oldColor = GUI.color;
        GUI.color = statusColor;
        GUILayout.Label($"Status: {statusText}", GUILayout.Height(20));
        GUI.color = oldColor;

        // Show script count
        GUILayout.Label($"Available scripts: {_availableScripts.Count}", GUILayout.Height(15));
    }



    private void RefreshScriptList()
    {
        _availableScripts = LuaManager.Instance.GetAvailableScripts();

        // Validate current selection
        if (!string.IsNullOrEmpty(_selectedScript) && !_availableScripts.Contains(_selectedScript))
        {
            _selectedScript = null;
            _selectedScriptIndex = -1;
        }
        else if (!string.IsNullOrEmpty(_selectedScript))
        {
            _selectedScriptIndex = _availableScripts.IndexOf(_selectedScript);
        }
    }

    /// <summary>
    /// Get the name of the currently selected script.
    /// </summary>
    public string? CurrentScript => _selectedScript;
}
