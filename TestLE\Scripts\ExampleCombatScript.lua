-- Simple Combat Monitor
-- Monitors combat state and enemies

local lastCheck = 0
local wasInCombat = false

function OnUpdate()
    local currentTime = Time()

    -- Check every 2 seconds
    if currentTime - lastCheck < 2.0 then
        return
    end

    lastCheck = currentTime

    if not IsPlayerAlive() then
        return
    end

    -- Enemy count
    local enemies = GetEnemies()
    if enemies and #enemies > 0 then
        Log("Enemies nearby: " .. #enemies)
    end

    -- Health warning
    local health = GetPlayerHealth()
    if health and health < 50 then
        Log("WARNING: Low health!")
    end
end
