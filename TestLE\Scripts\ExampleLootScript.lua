-- Simple Loot Monitor
-- Monitors ground items

local lastCheck = 0

function OnUpdate()
    local currentTime = Time()

    -- Check every 3 seconds
    if currentTime - lastCheck < 3.0 then
        return
    end

    lastCheck = currentTime

    if not IsPlayerAlive() then
        return
    end

    local groundItems = GetGroundItems()
    if groundItems and #groundItems > 0 then
        Log("Ground items nearby: " .. #groundItems)

        local playerPos = GetPlayerPosition()
        if playerPos then
            local closeItems = 0
            for i = 1, #groundItems do
                local item = groundItems[i]
                if item and item.transform then
                    local distance = Distance(playerPos, item.transform.position)
                    if distance < 10.0 then
                        closeItems = closeItems + 1
                    end
                end
            end

            if closeItems > 0 then
                Log("Close items (< 10 units): " .. closeItems)
            end
        end
    end
end
