using MelonLoader;

namespace TestLE.Scripting;

/// <summary>
/// Simple integration for Lua scripting.
/// </summary>
public static class SimpleIntegration
{
    /// <summary>
    /// Initialize the scripting system.
    /// Call this from your mod's OnApplicationStart.
    /// </summary>
    public static void Initialize()
    {
        LuaManager.Instance.Initialize();
    }

    /// <summary>
    /// Shutdown the scripting system.
    /// Call this from your mod's OnApplicationQuit.
    /// </summary>
    public static void Shutdown()
    {
        LuaManager.Instance.Shutdown();
    }

    /// <summary>
    /// Call a function in a script.
    /// </summary>
    public static void CallScript(string scriptName, string functionName, params object[] args)
    {
        LuaManager.Instance.CallFunction(scriptName, functionName, args);
    }

    /// <summary>
    /// Reload all scripts (useful for console commands).
    /// </summary>
    public static void ReloadScripts()
    {
        var manager = LuaManager.Instance;
        if (manager.IsInitialized)
        {
            manager.Shutdown();
            manager.Initialize();
            MelonLogger.Msg("Scripts reloaded");
        }
    }
}

/*
 * USAGE EXAMPLE:
 * 
 * In your main mod class:
 * 
 * public override void OnApplicationStart()
 * {
 *     SimpleIntegration.Initialize();
 * }
 * 
 * public override void OnApplicationQuit()
 * {
 *     SimpleIntegration.Shutdown();
 * }
 * 
 * public override void OnUpdate()
 * {
 *     // Call a script function every frame
 *     SimpleIntegration.CallScript("MyScript", "OnUpdate");
 *     
 *     // Reload scripts with F9
 *     if (Input.GetKeyDown(KeyCode.F9))
 *         SimpleIntegration.ReloadScripts();
 * }
 */
