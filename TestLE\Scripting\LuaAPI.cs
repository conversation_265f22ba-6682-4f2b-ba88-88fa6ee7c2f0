using Il2Cpp;
using <PERSON>onLoader;
using MoonSharp.Interpreter;
using TestLE.Types;
using TestLE.Utilities;
using UnityEngine;

namespace TestLE.Scripting;

/// <summary>
/// Simple API bridge for Lua scripts.
/// </summary>
public static class LuaAPI
{
    private static bool _typesRegistered = false;

    /// <summary>
    /// Register all necessary types with MoonSharp for proper interop.
    /// This should be called once before creating any scripts.
    /// </summary>
    public static void RegisterTypes()
    {
        if (_typesRegistered) return;

        try
        {
            // Unity basic types
            UserData.RegisterType<Vector3>();
            UserData.RegisterType<Transform>();
            UserData.RegisterType<GameObject>();
            UserData.RegisterType<Camera>();

            // Game-specific types that might be exposed to Lua
            UserData.RegisterType<LocalPlayer>();
            UserData.RegisterType<Enemy>();
            UserData.RegisterType<GroundItem>();
            UserData.RegisterType<WorldObjectClickListener>();

            // Collections (if needed)
            UserData.RegisterType<List<Enemy>>();
            UserData.RegisterType<List<GroundItem>>();
            UserData.RegisterType<List<WorldObjectClickListener>>();

            _typesRegistered = true;
            MelonLogger.Msg("MoonSharp types registered successfully");
        }
        catch (Exception ex)
        {
            MelonLogger.Error($"Failed to register MoonSharp types: {ex.Message}");
        }
    }

    public static void RegisterAPI(Script script)
    {
        // Ensure types are registered first
        RegisterTypes();

        // Work WITH MoonSharp - use proper registration methods
        var globals = script.Globals;

        // Game objects - let MoonSharp handle the conversion
        globals["GetPlayer"] = (Func<object?>)(() => PLAYER);
        globals["GetEnemies"] = (Func<object>)(() => ENEMIES);
        globals["GetGroundItems"] = (Func<object>)(() => GROUND_ITEMS);
        globals["GetInteractables"] = (Func<object>)(() => INTERACTABLES);

        // Player functions - return native types, let MoonSharp convert
        globals["GetPlayerPosition"] = (Func<Vector3?>)PlayerHelpers.GetPlayerPosition;
        globals["GetPlayerHealth"] = (Func<float?>)PlayerHelpers.GetPlayerHealth;
        globals["IsPlayerAlive"] = (Func<bool>)PlayerHelpers.IsPlayerAlive;
        globals["IsPlayerInCombat"] = (Func<bool>)(() => false); // Placeholder

        // Utility functions - work WITH MoonSharp's type system
        globals["Log"] = (Action<string>)(msg => MelonLogger.Msg($"[Script] {msg}"));
        globals["Distance"] = (Func<Vector3, Vector3, double>)((v1, v2) => Vector3.Distance(v1, v2));
        globals["Vector3"] = (Func<double, double, double, Vector3>)((x, y, z) => new Vector3((float)x, (float)y, (float)z));

        // Input functions
        globals["GetKey"] = (Func<string, bool>)Input.GetKey;
        globals["GetKeyDown"] = (Func<string, bool>)Input.GetKeyDown;

        // Time functions
        globals["Time"] = (Func<double>)(() => Time.time);
        globals["DeltaTime"] = (Func<double>)(() => Time.deltaTime);
        globals["FrameCount"] = (Func<int>)(() => Time.frameCount);

        // Random
        globals["Random"] = (Func<double>)(() => UnityEngine.Random.value);

        // Math utilities - use native C# functions
        globals["Sqrt"] = (Func<double, double>)Math.Sqrt;
        globals["Sin"] = (Func<double, double>)Math.Sin;
        globals["Cos"] = (Func<double, double>)Math.Cos;
        globals["Abs"] = (Func<double, double>)Math.Abs;
        globals["Min"] = (Func<double, double, double>)Math.Min;
        globals["Max"] = (Func<double, double, double>)Math.Max;

        // Coroutine functions
        RegisterCoroutineFunctions(script);
    }

    /// <summary>
    /// Register coroutine-related functions for Lua scripts
    /// </summary>
    private static void RegisterCoroutineFunctions(Script script)
    {
        var globals = script.Globals;

        // Basic yield - yields control back to the coroutine manager
        globals["yield"] = (Func<DynValue>)(() => DynValue.NewYieldReq(new YieldRequest()));

        // Wait for a specified number of seconds
        globals["waitForSeconds"] = (Func<double, DynValue>)((seconds) =>
        {
            var yieldRequest = new YieldRequest();
            yieldRequest.UserData = (float)seconds;
            return DynValue.NewYieldReq(yieldRequest);
        });

        // Wait for next frame (equivalent to yield in Unity)
        globals["waitForNextFrame"] = (Func<DynValue>)(() => DynValue.NewYieldReq(new YieldRequest()));

        // Utility function to check if we should yield (for manual yielding in loops)
        globals["shouldYield"] = (Func<bool>)(() => UnityEngine.Time.frameCount % 10 == 0); // Yield every 10 frames
    }
}
