-- Coroutine Loot Monitor
-- Monitors ground items using coroutines

function main()
    Log("Starting coroutine loot monitor...")
    
    while true do
        if not IsPlayerAlive() then
            Log("Player is dead, waiting...")
            waitForSeconds(5.0)
        else
            checkLoot()
            -- Check every 3 seconds
            waitForSeconds(3.0)
        end
    end
end

function checkLoot()
    local groundItems = GetGroundItems()
    if groundItems and #groundItems > 0 then
        Log("Ground items nearby: " .. #groundItems)

        local playerPos = GetPlayerPosition()
        if playerPos then
            local closeItems = 0
            for i = 1, #groundItems do
                local item = groundItems[i]
                if item and item.transform then
                    local distance = Distance(playerPos, item.transform.position)
                    if distance < 10.0 then
                        closeItems = closeItems + 1
                    end
                end
            end

            if closeItems > 0 then
                Log("Close items (< 10 units): " .. closeItems)
            end
        end
    end
end
